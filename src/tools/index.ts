import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { executeShellTool } from "./shell/index.js";
import { getSystemInfoTool } from "./get-system-info/index.js";
import { listAllowedCommandsTool } from "./list-allowed-commands/index.js";

// 工具列表
const tools = [
  executeShellTool,
  getSystemInfoTool,
  listAllowedCommandsTool
];

/**
 * 注册所有工具到 MCP 服务器
 */
export function registerTools(server: McpServer) {
  tools.forEach(tool => {
    server.registerTool(
      tool.name,
      tool.config,
      tool.handler
    );
  });

  // 工具注册完成，不输出日志
}

/**
 * 获取所有工具的信息
 */
export function getToolsInfo() {
  return tools.map(tool => ({
    name: tool.name,
    title: tool.config.title,
    description: tool.config.description
  }));
}
