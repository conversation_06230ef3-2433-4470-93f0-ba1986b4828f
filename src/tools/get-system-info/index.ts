import * as os from "os";

export const getSystemInfoTool = {
  name: "get-system-info",
  config: {
    title: "Get System Information",
    description: "Get basic system information",
    inputSchema: {}
  },
  handler: async (args: any) => {
    try {
      const info = {
        platform: os.platform(),
        arch: os.arch(),
        release: os.release(),
        hostname: os.hostname(),
        homedir: os.homedir(),
        tmpdir: os.tmpdir(),
        cwd: process.cwd(),
        nodeVersion: process.version,
        uptime: os.uptime()
      };

      let output = `🖥️  System Information\n\n`;
      output += `🏷️  Platform: ${info.platform}\n`;
      output += `🏗️  Architecture: ${info.arch}\n`;
      output += `📋 OS Release: ${info.release}\n`;
      output += `🏠 Hostname: ${info.hostname}\n`;
      output += `📁 Home Directory: ${info.homedir}\n`;
      output += `📂 Current Directory: ${info.cwd}\n`;
      output += `📦 Node.js Version: ${info.nodeVersion}\n`;
      output += `⏰ System Uptime: ${Math.floor(info.uptime / 3600)}h ${Math.floor((info.uptime % 3600) / 60)}m\n`;

      return {
        content: [{
          type: "text" as const,
          text: output
        }]
      };
    } catch (error: any) {
      return {
        content: [{
          type: "text" as const,
          text: `❌ Failed to get system information: ${error.message}`
        }],
        isError: true
      };
    }
  }
};
