export declare const getSystemInfoTool: {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema: {};
    };
    handler: (args: any) => Promise<{
        content: {
            type: "text";
            text: string;
        }[];
        isError?: undefined;
    } | {
        content: {
            type: "text";
            text: string;
        }[];
        isError: boolean;
    }>;
};
//# sourceMappingURL=index.d.ts.map