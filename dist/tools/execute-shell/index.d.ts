import { z } from "zod";
export declare const executeShellTool: {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema: {
            command: z.ZodString;
            workingDirectory: z.ZodOptional<z.ZodString>;
            timeout: z.ZodOptional<z.ZodNumber>;
        };
    };
    handler: (args: any) => Promise<{
        content: {
            type: "text";
            text: string;
        }[];
        isError: boolean;
    } | {
        content: {
            type: "text";
            text: string;
        }[];
        isError?: undefined;
    }>;
};
//# sourceMappingURL=index.d.ts.map