{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/tools/execute-shell/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAE1E,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAElC,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,IAAI,EAAE,eAAe;IACrB,MAAM,EAAE;QACN,KAAK,EAAE,uBAAuB;QAC9B,WAAW,EAAE,2DAA2D;QACxE,WAAW,EAAE;YACX,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;YAC5D,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8CAA8C,CAAC;YAChG,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;SACnF;KACF;IACD,OAAO,EAAE,KAAK,EAAE,IAAS,EAAE,EAAE;QAC3B,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;QACzD,IAAI,CAAC;YACH,OAAO;YACP,MAAM,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACtB,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAe;4BACrB,IAAI,EAAE,uBAAuB,WAAW,CAAC,MAAM,gBAAgB,OAAO,EAAE;yBACzE,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;YAED,SAAS;YACT,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBACpD,eAAe;gBACf,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAChF,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;oBACnE,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAe;gCACrB,IAAI,EAAE,0BAA0B,gBAAgB,kBAAkB;6BACnE,CAAC;wBACF,OAAO,EAAE,IAAI;qBACd,CAAC;gBACJ,CAAC;gBACD,GAAG,GAAG,YAAY,CAAC;YACrB,CAAC;YAED,SAAS;YACT,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,EAAE,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEjF,OAAO;YACP,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE;gBAClD,GAAG;gBACH,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,eAAe,CAAC,eAAe;aAC3C,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,OAAO;YACP,IAAI,MAAM,GAAG,mCAAmC,CAAC;YACjD,MAAM,IAAI,yBAAyB,GAAG,IAAI,CAAC;YAC3C,MAAM,IAAI,uBAAuB,aAAa,MAAM,CAAC;YACrD,MAAM,IAAI,eAAe,OAAO,MAAM,CAAC;YAEvC,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,eAAe,MAAM,IAAI,CAAC;YACtC,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,gBAAgB,MAAM,IAAI,CAAC;YACvC,CAAC;YAED,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;gBACvB,MAAM,IAAI,0BAA0B,CAAC;YACvC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAe;wBACrB,IAAI,EAAE,MAAM;qBACb,CAAC;aACH,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,YAAY,GAAG,8BAA8B,CAAC;YAClD,YAAY,IAAI,eAAe,OAAO,IAAI,CAAC;YAE3C,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC/B,YAAY,IAAI,sCAAsC,OAAO,YAAY,CAAC;YAC5E,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBACtB,YAAY,IAAI,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAAC;YAClD,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,YAAY,IAAI,eAAe,KAAK,CAAC,MAAM,IAAI,CAAC;YAClD,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,YAAY,IAAI,gBAAgB,KAAK,CAAC,MAAM,IAAI,CAAC;YACnD,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACpD,YAAY,IAAI,qBAAqB,KAAK,CAAC,OAAO,IAAI,CAAC;YACzD,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAe;wBACrB,IAAI,EAAE,YAAY;qBACnB,CAAC;gBACF,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC"}