{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/tools/list-allowed-commands/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAE3D,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,IAAI,EAAE,uBAAuB;IAC7B,MAAM,EAAE;QACN,KAAK,EAAE,uBAAuB;QAC9B,WAAW,EAAE,iDAAiD;QAC9D,WAAW,EAAE,EAAE;KAChB;IACD,OAAO,EAAE,KAAK,EAAE,IAAS,EAAE,EAAE;QAC3B,IAAI,MAAM,GAAG,+BAA+B,CAAC;QAE7C,MAAM,IAAI,uBAAuB,CAAC;QAClC,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC5C,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,2BAA2B,CAAC;QACtC,eAAe,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC9C,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,iBAAiB,CAAC;QAC5B,MAAM,IAAI,2BAA2B,eAAe,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAC;QAClF,MAAM,IAAI,0BAA0B,eAAe,CAAC,eAAe,UAAU,CAAC;QAE9E,OAAO;YACL,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAe;oBACrB,IAAI,EAAE,MAAM;iBACb,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC"}