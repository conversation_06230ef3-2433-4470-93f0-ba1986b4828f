import { z } from "zod";
/**
 * 执行 shell 命令的处理函数
 */
declare function executeShellHandler(args: any): Promise<{
    content: {
        type: "text";
        text: string;
    }[];
    isError?: undefined;
} | {
    content: {
        type: "text";
        text: string;
    }[];
    isError: boolean;
}>;
export declare const executeShellTool: {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema: z.ZodObject<{
            command: z.ZodString;
            timeout: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        }, "strip", z.ZodTypeAny, {
            command: string;
            timeout: number;
        }, {
            command: string;
            timeout?: number | undefined;
        }>;
    };
    handler: typeof executeShellHandler;
};
export {};
//# sourceMappingURL=index.d.ts.map