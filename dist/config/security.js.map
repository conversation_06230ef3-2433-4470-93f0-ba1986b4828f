{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/config/security.ts"], "names": [], "mappings": "AAAA,OAAO;AACP,MAAM,CAAC,MAAM,eAAe,GAAG;IAC7B,eAAe;IACf,eAAe,EAAE;QACf,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM;QAChF,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS;QACtE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM;QACxD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK;KAC1C;IACD,aAAa;IACb,iBAAiB,EAAE;QACjB,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;QACnE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU;QACpE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;KACnD;IACD,aAAa;IACb,gBAAgB,EAAE,KAAK;IACvB,SAAS;IACT,eAAe,EAAE,KAAK;CACvB,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,OAAe;IAC3C,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAEtC,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;IAClD,CAAC;IAED,aAAa;IACb,MAAM,iBAAiB,GAAG;QACxB,aAAa,EAAG,SAAS;QACzB,MAAM,EAAU,OAAO;QACvB,GAAG,EAAa,MAAM;QACtB,IAAI,EAAY,gBAAgB;KACjC,CAAC;IAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;QACxC,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,+BAA+B,EAAE,CAAC;QAClE,CAAC;IACH,CAAC;IAED,kBAAkB;IAClB,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACjD,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAEpC,aAAa;IACb,IAAI,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CACrD,WAAW,KAAK,SAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;QACtE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,WAAW,gBAAgB,EAAE,CAAC;IAC1E,CAAC;IAED,uBAAuB;IACvB,IAAI,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/C,MAAM,SAAS,GAAG,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC/D,WAAW,KAAK,OAAO,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC;QAElE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,WAAW,0BAA0B,EAAE,CAAC;QACpF,CAAC;IACH,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACxB,CAAC"}