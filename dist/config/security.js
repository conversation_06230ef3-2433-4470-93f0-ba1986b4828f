// 安全配置
export const SECURITY_CONFIG = {
    // 允许的命令前缀（白名单）
    allowedCommands: [
        "ls", "pwd", "echo", "cat", "head", "tail", "grep", "find", "wc", "sort", "uniq",
        "date", "whoami", "uname", "df", "du", "ps", "top", "which", "whereis",
        "git", "npm", "node", "python", "python3", "pip", "pip3",
        "curl", "wget", "ping", "nslookup", "dig"
    ],
    // 禁止的命令（黑名单）
    forbiddenCommands: [
        "rm", "rmdir", "mv", "cp", "chmod", "chown", "sudo", "su", "passwd",
        "kill", "killall", "pkill", "shutdown", "reboot", "halt", "poweroff",
        "dd", "fdisk", "mkfs", "mount", "umount", "format"
    ],
    // 最大执行时间（毫秒）
    maxExecutionTime: 30000,
    // 最大输出长度
    maxOutputLength: 10000
};
/**
 * 检查命令是否安全
 */
export function isCommandSafe(command) {
    const trimmedCommand = command.trim();
    if (!trimmedCommand) {
        return { safe: false, reason: "Empty command" };
    }
    // 检查是否包含危险字符
    const dangerousPatterns = [
        /[;&|`$(){}]/, // 命令注入字符
        /\.\./, // 路径遍历
        />/, // 重定向
        /<</, // Here document
    ];
    for (const pattern of dangerousPatterns) {
        if (pattern.test(trimmedCommand)) {
            return { safe: false, reason: "Contains dangerous characters" };
        }
    }
    // 获取命令的第一个词（实际命令）
    const commandParts = trimmedCommand.split(/\s+/);
    const baseCommand = commandParts[0];
    // 检查是否在禁止列表中
    if (SECURITY_CONFIG.forbiddenCommands.some(forbidden => baseCommand === forbidden || baseCommand.endsWith(`/${forbidden}`))) {
        return { safe: false, reason: `Command '${baseCommand}' is forbidden` };
    }
    // 检查是否在允许列表中（如果配置了白名单）
    if (SECURITY_CONFIG.allowedCommands.length > 0) {
        const isAllowed = SECURITY_CONFIG.allowedCommands.some(allowed => baseCommand === allowed || baseCommand.endsWith(`/${allowed}`));
        if (!isAllowed) {
            return { safe: false, reason: `Command '${baseCommand}' is not in allowed list` };
        }
    }
    return { safe: true };
}
//# sourceMappingURL=security.js.map