<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing - Simple, easy pricing</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500&family=Inter:wght@400;500&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #FFFFFF;
            font-family: 'IBM Plex Sans', sans-serif;
            min-height: 100vh;
        }

        .pricing-container {
            padding: 103px 231px;
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 61px;
        }

        .title-section {
            text-align: center;
            width: 723px;
            margin: 0 auto;
        }

        .main-title {
            font-size: 60px;
            font-weight: 400;
            line-height: 1.1em;
            margin-bottom: 20px;
        }

        .subtitle {
            font-family: 'Inter', sans-serif;
            font-size: 18px;
            font-weight: 400;
            line-height: 1.56em;
            color: #A1A1AA;
        }

        .progress-section {
            width: 1136px;
            height: 56px;
            position: relative;
            margin: 0 auto;
        }

        .progress-bg {
            position: absolute;
            top: 43px;
            width: 100%;
            height: 9px;
            background-color: #27272A;
            border-radius: 60px;
        }

        .progress-fill {
            position: absolute;
            top: 44px;
            width: 278px;
            height: 7px;
            background: linear-gradient(135deg, #6DDCFF 0%, #7F60F9 100%);
            border-radius: 60px;
        }

        .progress-markers {
            position: absolute;
            top: 39px;
            width: 100%;
            height: 17px;
        }

        .progress-marker {
            position: absolute;
            width: 1px;
            height: 17px;
            background-color: #3F3F46;
        }

        .progress-slider {
            position: absolute;
            left: 242px;
            top: 0;
            width: 72px;
            height: 56px;
        }

        .slider-handle {
            position: absolute;
            left: 27px;
            top: 39px;
            width: 17px;
            height: 17px;
            background-color: #000000;
            border: 4px solid;
            border-image: linear-gradient(135deg, #6DDCFF 0%, #7F60F9 100%) 1;
            border-radius: 50%;
        }

        .slider-tooltip {
            position: absolute;
            top: 0;
            left: 0;
            width: 72px;
            height: 30px;
            background-color: #27272A;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            line-height: 1.62em;
        }

        .pricing-cards {
            display: flex;
            gap: 30px;
            width: 1137px;
            margin: 0 auto;
        }

        .pricing-card {
            width: 359px;
            height: 598px;
            background-color: #18181B;
            border-radius: 6px;
            padding: 31px 35px 34px;
            position: relative;
        }

        .pricing-card.featured {
            border: 2px solid;
            border-image: linear-gradient(135deg, #6DDCFF 0%, #7F60F9 100%) 1;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 400;
            line-height: 1.67em;
            margin-bottom: 14px;
        }

        .card-price {
            margin-bottom: 10px;
        }

        .price-amount {
            font-size: 60px;
            font-weight: 400;
            line-height: 1.1em;
            display: inline;
        }

        .price-period {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5em;
            color: #71717A;
            margin-left: 8px;
        }

        .card-description {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5em;
            color: #A1A1AA;
            margin-bottom: 30px;
        }

        .divider {
            width: 291px;
            height: 1px;
            background-color: #27272A;
            margin: 30px 0;
        }

        .features-list {
            list-style: none;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 20px;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.5em;
        }

        .feature-item:last-child {
            margin-bottom: 0;
        }

        .check-icon {
            width: 20px;
            height: 20px;
            color: #71717A;
        }

        .info-icon {
            width: 18px;
            height: 18px;
            color: #71717A;
            margin-left: auto;
        }

        .cta-button {
            width: 292px;
            height: 55px;
            background: linear-gradient(135deg, #6DDCFF 0%, #7F60F9 100%);
            border: 1px solid;
            border-image: linear-gradient(135deg, #6DDCFF 0%, #7F60F9 100%) 1;
            border-radius: 5px;
            color: #FFFFFF;
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5em;
            cursor: pointer;
            transition: opacity 0.2s;
            position: absolute;
            bottom: 34px;
            left: 34px;
        }

        .cta-button:hover {
            opacity: 0.9;
        }

        .cta-button.secondary {
            background: #18181B;
            border: 1px solid;
            border-image: linear-gradient(135deg, #6DDCFF 0%, #7F60F9 100%) 1;
        }

        @media (max-width: 1400px) {
            .pricing-container {
                padding: 50px 50px;
            }

            .pricing-cards {
                flex-direction: column;
                align-items: center;
                gap: 30px;
            }

            .progress-section {
                width: 100%;
                max-width: 800px;
            }
        }
    </style>
</head>
<body>
    <div class="pricing-container">
        <!-- Title Section -->
        <div class="title-section">
            <h1 class="main-title">Simple, easy pricing</h1>
            <p class="subtitle">Amet minim mollit non deserunt ullamco.</p>
        </div>

        <!-- Progress Bar Section -->
        <div class="progress-section">
            <div class="progress-bg"></div>
            <div class="progress-fill"></div>
            <div class="progress-markers">
                <div class="progress-marker" style="left: 106px;"></div>
                <div class="progress-marker" style="left: 278px;"></div>
                <div class="progress-marker" style="left: 450px;"></div>
                <div class="progress-marker" style="left: 622px;"></div>
                <div class="progress-marker" style="left: 794px;"></div>
                <div class="progress-marker" style="left: 966px;"></div>
            </div>
            <div class="progress-slider">
                <div class="slider-tooltip">100 users</div>
                <div class="slider-handle"></div>
            </div>
        </div>

        <!-- Pricing Cards -->
        <div class="pricing-cards">
            <!-- Personal Plan -->
            <div class="pricing-card">
                <div class="card-header">
                    <h3>Personal</h3>
                </div>
                <div class="card-price">
                    <span class="price-amount">$19</span>
                    <span class="price-period">/ month</span>
                </div>
                <p class="card-description">All the basic features to boost your freelance career</p>
                <div class="divider"></div>
                <ul class="features-list">
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Full Access to Landingfolio</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>100 GB Free Storage</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Unlimited Visitors</span>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>10 Agents</span>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Live Chat Support</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                </ul>
                <button class="cta-button secondary">Get 14 Days Free Trial</button>
            </div>

            <!-- Professional Plan (Featured) -->
            <div class="pricing-card featured">
                <div class="card-header">
                    <h3>Professional</h3>
                </div>
                <div class="card-price">
                    <span class="price-amount">$49</span>
                    <span class="price-period">/ month</span>
                </div>
                <p class="card-description">All the basic features to boost your freelance career</p>
                <div class="divider"></div>
                <ul class="features-list">
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Full Access to Landingfolio</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>100 GB Free Storage</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Unlimited Visitors</span>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>10 Agents</span>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Live Chat Support</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                </ul>
                <button class="cta-button">Get 14 Days Free Trial</button>
            </div>

            <!-- Business Plan -->
            <div class="pricing-card">
                <div class="card-header">
                    <h3>Business</h3>
                </div>
                <div class="card-price">
                    <span class="price-amount">$99</span>
                    <span class="price-period">/ month</span>
                </div>
                <p class="card-description">All the basic features to boost your freelance career</p>
                <div class="divider"></div>
                <ul class="features-list">
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Full Access to Landingfolio</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>100 GB Free Storage</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Unlimited Visitors</span>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>10 Agents</span>
                    </li>
                    <li class="feature-item">
                        <svg class="check-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Live Chat Support</span>
                        <svg class="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </li>
                </ul>
                <button class="cta-button secondary">Get 14 Days Free Trial</button>
            </div>
        </div>
    </div>
</body>
</html>
